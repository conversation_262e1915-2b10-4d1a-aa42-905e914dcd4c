/**
 * @flow strict
 */

import type { Icon } from './index';

export type FontistoGlyphs = 'acrobat-reader' | 'applemusic' | 'atlassian' | 'aws' | 'baidu' | 'bing' | 'bower' | 'dailymotion' | 'delicious' | 'deviantart' | 'disqus' | 'flipboard' | 'graphql' | 'hexo' | 'hipchat' | 'icq' | 'invision' | 'jekyll' | 'jira' | 'json' | 'livestream' | 'messenger' | 'meteor' | 'onenote' | 'mongodb' | 'netflix' | 'nginx' | 'odnoklassniki' | 'onedrive' | 'origin' | 'pingdom' | 'rails' | 'raspberry-pi' | 'redis' | 'redux' | 'saucelabs' | 'scorp' | 'sentry' | 'shazam' | 'shopify' | 'sinaweibo' | 'slides' | 'sublimetext' | 'swift' | 'ted' | 'telegram' | 'tesla' | 'tinder' | 'treehouse' | 'twoo' | 'udacity' | 'webstorm' | 'wix' | 'yandex-international' | 'yandex' | 'ember' | 'cpanel' | 'viber' | 'deskpro' | 'discord' | 'discourse' | 'adobe' | 'algolia' | 'atom' | 'babel' | 'coffeescript' | 'electronjs' | 'mysql' | 'oracle' | 'php' | 'sourcetree' | 'ubuntu' | 'unity' | 'unreal-engine' | 'webpack' | 'angelist' | 'app-store' | 'digg' | 'dockers' | 'envato' | 'gitlab' | 'google-drive' | 'google-play' | 'grunt' | 'gulp' | 'hacker-news' | 'imdb' | 'jenkins' | 'joomla' | 'kickstarter' | 'laravel' | 'less' | 'line' | 'npm' | 'periscope' | 'product-hunt' | 'quora' | 'skyatlas' | 'stylus' | 'travis' | 'trello' | 'uber' | 'vine' | 'visual-studio' | 'vk' | 'vuejs' | 'microsoft' | 'blogger' | '500px' | 'amazon' | 'ampproject' | 'android' | 'angularjs' | 'apple' | 'behance' | 'bitbucket' | 'bluetooth-b' | 'cloudflare' | 'codepen' | 'css3' | 'dribbble' | 'dropbox' | 'facebook' | 'flickr' | 'foursquare' | 'git' | 'github' | 'google-plus' | 'google' | 'hangout' | 'houzz' | 'html5' | 'instagram' | 'java' | 'jquery' | 'jsfiddle' | 'linkedin' | 'linux' | 'magento' | 'maxcdn' | 'medium' | 'meetup' | 'nodejs' | 'opencart' | 'pinterest' | 'playstation' | 'python' | 'react' | 'reddit' | 'ruby' | 'sass' | 'skype' | 'slack' | 'snapchat' | 'soundcloud' | 'spotify' | 'stack-overflow' | 'steam' | 'stumbleupon' | 'svn' | 'swarm' | 'tripadvisor' | 'tumblr' | 'twitch' | 'twitter' | 'vimeo' | 'wetransfer' | 'whatsapp' | 'wifi-logo' | 'wikipedia' | 'windows' | 'wordpress' | 'xbox' | 'yahoo' | 'yelp' | 'youtube-play' | 'cocoapods' | 'composer' | 'yarn' | 'language' | 'toggle-off' | 'toggle-on' | 'anchor' | 'archive' | 'at' | 'ban' | 'battery-half' | 'battery-full' | 'battery-empty' | 'battery-quarter' | 'battery-three-quarters' | 'bell-alt' | 'bell' | 'bookmark-alt' | 'bookmark' | 'bug' | 'calculator' | 'calendar' | 'crosshairs' | 'desktop' | 'download' | 'film' | 'history' | 'hourglass-end' | 'hourglass-half' | 'hourglass-start' | 'hourglass' | 'info' | 'key' | 'keyboard' | 'laptop' | 'lightbulb' | 'magnet' | 'map-marker-alt' | 'map-marker' | 'map' | 'mobile-alt' | 'mobile' | 'paw' | 'phone' | 'power' | 'qrcode' | 'question' | 'search' | 'sitemap' | 'star-half' | 'stopwatch' | 'tablet-alt' | 'tablet' | 'ticket' | 'tv' | 'upload' | 'user-secret' | 'camera' | 'clock' | 'close-a' | 'code' | 'comment' | 'commenting' | 'comments' | 'crop' | 'cursor' | 'database' | 'date' | 'earth' | 'email' | 'eye' | 'female' | 'favorite' | 'filter' | 'fire' | 'flag' | 'flash' | 'home' | 'link' | 'locked' | 'male' | 'minus-a' | 'more-v-a' | 'more-v' | 'move-h-a' | 'move-h' | 'nav-icon-a' | 'nav-icon-grid-a' | 'nav-icon-grid' | 'nav-icon-list-a' | 'nav-icon-list' | 'nav-icon' | 'navigate' | 'paper-plane' | 'person' | 'persons' | 'picture' | 'plus-a' | 'print' | 'quote-a-left' | 'quote-a-right' | 'quote-left' | 'quote-right' | 'reply' | 'rss' | 'scissors' | 'share-a' | 'share' | 'trash' | 'unlocked' | 'usb' | 'wifi' | 'world-o' | 'world' | 'zoom' | 'adjust' | 'recycle' | 'pinboard' | 'zoom-minus' | 'zoom-plus' | 'check' | 'asterisk' | 'hashtag' | 'checkbox-active' | 'checkbox-passive' | 'radio-btn-active' | 'radio-btn-passive' | 'shopping-bag-1' | 'shopping-bag' | 'shopping-barcode' | 'shopping-basket-add' | 'shopping-basket-remove' | 'shopping-basket' | 'shopping-package' | 'shopping-pos-machine' | 'shopping-sale' | 'shopping-store' | 'angle-dobule-down' | 'angle-dobule-left' | 'angle-dobule-right' | 'angle-dobule-up' | 'angle-down' | 'angle-left' | 'angle-right' | 'angle-up' | 'arrow-down-l' | 'arrow-down' | 'arrow-expand' | 'arrow-h' | 'arrow-left-l' | 'arrow-left' | 'arrow-move' | 'arrow-resize' | 'arrow-return-left' | 'arrow-return-right' | 'arrow-right-l' | 'arrow-right' | 'arrow-swap' | 'arrow-up-l' | 'arrow-up' | 'arrow-v' | 'caret-down' | 'caret-left' | 'caret-right' | 'caret-up' | 'fi' | 'fontisto' | 'backward' | 'eject' | 'equalizer' | 'forward' | 'headphone' | 'heart' | 'mic' | 'music-note' | 'pause' | 'play-list' | 'play' | 'player-settings' | 'podcast' | 'random' | 'record' | 'star' | 'step-backwrad' | 'step-forward' | 'stop' | 'volume-down' | 'volume-mute' | 'volume-off' | 'volume-up' | 'airplay' | 'bold' | 'broken-link' | 'center-align' | 'close' | 'columns' | 'copy' | 'eraser' | 'export' | 'file-1' | 'file-2' | 'folder' | 'font' | 'import' | 'indent' | 'italic' | 'justify' | 'left-align' | 'link2' | 'list-1' | 'list-2' | 'outdent' | 'paperclip' | 'paragraph' | 'paste' | 'preview' | 'print2' | 'redo' | 'right-align' | 'save-1' | 'save' | 'scissors2' | 'strikethrough' | 'subscript' | 'superscript' | 'table-1' | 'table-2' | 'text-height' | 'text-width' | 'underline' | 'undo' | 'cloud-down' | 'cloud-refresh' | 'cloud-up' | 'cloudy-gusts' | 'cloudy' | 'compass' | 'day-cloudy' | 'day-haze' | 'day-lightning' | 'day-rain' | 'day-snow' | 'day-sunny' | 'fog' | 'horizon-alt' | 'horizon' | 'lightning' | 'lightnings' | 'night-alt-cloudy' | 'night-alt-lightning' | 'night-alt-rain' | 'night-alt-snow' | 'night-clear' | 'rain' | 'rainbow' | 'rains' | 'snow' | 'snows' | 'thermometer' | 'umbrella' | 'wind' | 'confused' | 'dizzy' | 'expressionless' | 'frowning' | 'heart-eyes' | 'laughing' | 'mad' | 'nervous' | 'neutral' | 'open-mouth' | 'rage' | 'slightly-smile' | 'smiley' | 'smiling' | 'stuck-out-tongue' | 'sunglasses' | 'surprised' | 'tongue' | 'wink' | 'zipper-mouth' | 'aids' | 'ambulance' | 'bandage' | 'bed-patient' | 'blood-drop' | 'blood-test' | 'blood' | 'dna' | 'doctor' | 'drug-pack' | 'first-aid-alt' | 'heart-alt' | 'heartbeat-alt' | 'heartbeat' | 'helicopter-ambulance' | 'hospital' | 'injection-syringe' | 'laboratory' | 'nurse' | 'nursing-home' | 'paralysis-disability' | 'pills' | 'prescription' | 'pulse' | 'stethoscope' | 'surgical-knife' | 'tablets' | 'test-bottle' | 'test-tube-alt' | 'test-tube' | 'thermometer-alt' | 'american-express' | 'credit-card' | 'google-wallet' | 'iyzigo' | 'mastercard' | 'paypal-p' | 'paypal' | 'payu' | 'troy' | 'visa' | 'dinners-club' | 'apple-pay' | 'discover' | 'jcb' | 'dislike' | 'like' | 'audio-description' | 'blind' | 'braille' | 'deaf' | 'fa-american-sign-language-interpreting' | 'low-vision' | 'tty' | 'universal-acces' | 'wheelchair' | 'area-chart' | 'bar-chart' | 'line-chart' | 'pie-chart-1' | 'pie-chart-2' | 'chrome' | 'edge' | 'firefox' | 'internet-explorer' | 'opera' | 'safari' | 'bitcoin' | 'dollar' | 'euro' | 'gbp' | 'gg' | 'ils' | 'inr' | 'krw' | 'rouble' | 'tl' | 'yen' | 'genderless' | 'intersex' | 'mars-double' | 'mars-stroke-h' | 'mars-stroke-v' | 'mars-stroke' | 'mars' | 'mercury' | 'neuter' | 'transgender-alt' | 'transgender' | 'venus-double' | 'venus-mars' | 'venus' | 'automobile' | 'bicycle' | 'bus' | 'car' | 'helicopter' | 'metro' | 'motorcycle' | 'plane' | 'rocket' | 'ship' | 'subway' | 'taxi' | 'train' | 'truck' | 'yacht' | 'beach-slipper' | 'bus-ticket' | 'cocktail' | 'compass-alt' | 'direction-sign' | 'do-not-disturb' | 'flotation-ring' | 'holiday-village' | 'hot-air-balloon' | 'hotel-alt' | 'hotel' | 'island' | 'money-symbol' | 'parasol' | 'passport-alt' | 'passport' | 'photograph' | 'plane-ticket' | 'room' | 'sait-boat' | 'snorkel' | 'suitcase-alt' | 'suitcase' | 'sun' | 'sunglasses-alt' | 'swimsuit' | 'tent' | 'ticket-alt' | 'train-ticket' | 'wallet' | 'circle-o-notch' | 'propeller-1' | 'propeller-2' | 'propeller-3' | 'propeller-4' | 'spinner-cog' | 'spinner-fidget' | 'spinner-refresh' | 'spinner-rotate-forward' | 'spinner' | 'snowflake' | 'snowflake-1' | 'snowflake-2' | 'snowflake-3' | 'snowflake-4' | 'snowflake-5' | 'snowflake-6' | 'snowflake-7' | 'snowflake-8' | 'curve' | 'ellipse' | 'rectangle' | 'shield';

declare export default Class<Icon<FontistoGlyphs>>;
