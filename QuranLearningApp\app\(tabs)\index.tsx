import React, { useEffect } from "react";
import {
  StyleSheet,
  ScrollView,
  View,
  Text,
  TouchableOpacity,
  Dimensions,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { useRouter } from "expo-router";
import { useAppSelector, useAppDispatch } from "@/src/store";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { setUser } from "@/src/store/slices/userSlice";
import { setSkillTree } from "@/src/store/slices/lessonSlice";
import { setUserProgress } from "@/src/store/slices/progressSlice";
import { setAchievements } from "@/src/store/slices/achievementSlice";
import {
  sampleUser,
  sampleSkillTree,
  getSampleUserProgress,
  sampleAchievements,
} from "@/src/data/sampleData";

const { width } = Dimensions.get("window");

export default function HomeScreen() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const user = useAppSelector((state) => state.user.currentUser);
  const progress = useAppSelector((state) => state.progress);

  // Initialize sample data on first load
  useEffect(() => {
    if (!user) {
      dispatch(setUser(sampleUser));
      dispatch(setSkillTree(sampleSkillTree));
      dispatch(setUserProgress(sampleUser.progress));
      dispatch(setAchievements(sampleAchievements));
    }
  }, [user, dispatch]);

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header Section */}
      <LinearGradient colors={["#4CAF50", "#2E7D32"]} style={styles.header}>
        <View style={styles.headerContent}>
          <Text style={styles.greeting}>
            السلام عليكم {user?.name || "Student"}!
          </Text>
          <Text style={styles.subtitle}>
            Ready to continue your Quran journey?
          </Text>

          {/* Streak Counter */}
          <View style={styles.streakContainer}>
            <Text style={styles.streakNumber}>{user?.streak || 0}</Text>
            <Text style={styles.streakLabel}>Day Streak 🔥</Text>
          </View>
        </View>
      </LinearGradient>

      {/* Daily Goal Progress */}
      <ThemedView style={styles.goalSection}>
        <ThemedText type="subtitle">Today's Goal</ThemedText>
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View
              style={[
                styles.progressFill,
                {
                  width: `${Math.min(
                    (progress.todayProgress / progress.dailyGoal) * 100,
                    100
                  )}%`,
                },
              ]}
            />
          </View>
          <Text style={styles.progressText}>
            {progress.todayProgress}/{progress.dailyGoal} minutes
          </Text>
        </View>
      </ThemedView>

      {/* Quick Actions */}
      <ThemedView style={styles.quickActions}>
        <ThemedText type="subtitle">Continue Learning</ThemedText>
        <View style={styles.actionGrid}>
          <TouchableOpacity
            style={styles.actionCard}
            onPress={() => router.push("/lesson/lesson_1")}
          >
            <Text style={styles.actionIcon}>📖</Text>
            <Text style={styles.actionTitle}>Daily Lesson</Text>
            <Text style={styles.actionSubtitle}>5 min</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionCard}
            onPress={() => router.push("/(tabs)/practice")}
          >
            <Text style={styles.actionIcon}>🎯</Text>
            <Text style={styles.actionTitle}>Practice</Text>
            <Text style={styles.actionSubtitle}>Recitation</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionCard}
            onPress={() => router.push("/(tabs)/learn")}
          >
            <Text style={styles.actionIcon}>🏆</Text>
            <Text style={styles.actionTitle}>Challenges</Text>
            <Text style={styles.actionSubtitle}>Weekly</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionCard}
            onPress={() => router.push("/(tabs)/community")}
          >
            <Text style={styles.actionIcon}>👥</Text>
            <Text style={styles.actionTitle}>Community</Text>
            <Text style={styles.actionSubtitle}>Study Groups</Text>
          </TouchableOpacity>
        </View>
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  header: {
    paddingTop: 60,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerContent: {
    alignItems: "center",
  },
  greeting: {
    fontSize: 24,
    fontWeight: "bold",
    color: "white",
    textAlign: "center",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: "rgba(255, 255, 255, 0.9)",
    textAlign: "center",
    marginBottom: 20,
  },
  streakContainer: {
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: 15,
    paddingVertical: 10,
    paddingHorizontal: 20,
    alignItems: "center",
  },
  streakNumber: {
    fontSize: 32,
    fontWeight: "bold",
    color: "white",
  },
  streakLabel: {
    fontSize: 14,
    color: "white",
    marginTop: 4,
  },
  goalSection: {
    margin: 20,
    padding: 20,
    borderRadius: 15,
  },
  progressContainer: {
    marginTop: 15,
  },
  progressBar: {
    height: 8,
    backgroundColor: "#e0e0e0",
    borderRadius: 4,
    overflow: "hidden",
  },
  progressFill: {
    height: "100%",
    backgroundColor: "#4CAF50",
    borderRadius: 4,
  },
  progressText: {
    textAlign: "center",
    marginTop: 8,
    fontSize: 14,
    color: "#666",
  },
  quickActions: {
    margin: 20,
    padding: 20,
    borderRadius: 15,
  },
  actionGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    marginTop: 15,
  },
  actionCard: {
    width: (width - 80) / 2,
    backgroundColor: "white",
    borderRadius: 12,
    padding: 20,
    alignItems: "center",
    marginBottom: 15,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  actionIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 4,
  },
  actionSubtitle: {
    fontSize: 12,
    color: "#666",
  },
});
