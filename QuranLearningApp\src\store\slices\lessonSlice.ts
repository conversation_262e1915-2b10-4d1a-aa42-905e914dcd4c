import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Lesson, SkillTreeNode, Exercise } from '../../types';

interface LessonState {
  skillTree: SkillTreeNode[];
  currentLesson: Lesson | null;
  currentExercise: Exercise | null;
  exerciseIndex: number;
  lessonProgress: number;
  isLoading: boolean;
  error: string | null;
}

const initialState: LessonState = {
  skillTree: [],
  currentLesson: null,
  currentExercise: null,
  exerciseIndex: 0,
  lessonProgress: 0,
  isLoading: false,
  error: null,
};

const lessonSlice = createSlice({
  name: 'lessons',
  initialState,
  reducers: {
    setSkillTree: (state, action: PayloadAction<SkillTreeNode[]>) => {
      state.skillTree = action.payload;
    },
    unlockSkillNode: (state, action: PayloadAction<string>) => {
      const node = state.skillTree.find(n => n.id === action.payload);
      if (node) {
        node.isUnlocked = true;
      }
    },
    completeSkillNode: (state, action: PayloadAction<string>) => {
      const node = state.skillTree.find(n => n.id === action.payload);
      if (node) {
        node.isCompleted = true;
        // Unlock dependent nodes
        state.skillTree.forEach(n => {
          if (n.prerequisiteIds.includes(action.payload)) {
            n.isUnlocked = true;
          }
        });
      }
    },
    setCurrentLesson: (state, action: PayloadAction<Lesson>) => {
      state.currentLesson = action.payload;
      state.exerciseIndex = 0;
      state.lessonProgress = 0;
      state.currentExercise = action.payload.content.exercises[0] || null;
    },
    nextExercise: (state) => {
      if (state.currentLesson && state.exerciseIndex < state.currentLesson.content.exercises.length - 1) {
        state.exerciseIndex += 1;
        state.currentExercise = state.currentLesson.content.exercises[state.exerciseIndex];
        state.lessonProgress = ((state.exerciseIndex + 1) / state.currentLesson.content.exercises.length) * 100;
      }
    },
    previousExercise: (state) => {
      if (state.exerciseIndex > 0) {
        state.exerciseIndex -= 1;
        state.currentExercise = state.currentLesson?.content.exercises[state.exerciseIndex] || null;
        state.lessonProgress = ((state.exerciseIndex + 1) / (state.currentLesson?.content.exercises.length || 1)) * 100;
      }
    },
    completeLesson: (state) => {
      if (state.currentLesson) {
        state.currentLesson.isCompleted = true;
        state.lessonProgress = 100;
      }
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const {
  setSkillTree,
  unlockSkillNode,
  completeSkillNode,
  setCurrentLesson,
  nextExercise,
  previousExercise,
  completeLesson,
  setLoading,
  setError,
} = lessonSlice.actions;

export default lessonSlice.reducer;
