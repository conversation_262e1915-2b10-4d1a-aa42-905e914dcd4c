import React, { useState } from 'react';
import { StyleSheet, ScrollView, View, Text, TouchableOpacity, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';

const { width } = Dimensions.get('window');

export default function PracticeScreen() {
  const [selectedMode, setSelectedMode] = useState<string | null>(null);

  const practiceTypes = [
    {
      id: 'recitation',
      title: 'Recitation Practice',
      icon: '🎤',
      description: 'Practice your pronunciation',
      color: '#FF6B6B',
      difficulty: 'All Levels',
    },
    {
      id: 'listening',
      title: 'Listening Exercise',
      icon: '👂',
      description: 'Improve your listening skills',
      color: '#4ECDC4',
      difficulty: 'Beginner',
    },
    {
      id: 'translation',
      title: 'Translation Quiz',
      icon: '🔤',
      description: 'Test your understanding',
      color: '#45B7D1',
      difficulty: 'Intermediate',
    },
    {
      id: 'tajwid',
      title: 'Tajwid Rules',
      icon: '🎵',
      description: 'Master pronunciation rules',
      color: '#96CEB4',
      difficulty: 'Advanced',
    },
  ];

  const recentSessions = [
    {
      id: 1,
      type: 'Recitation',
      surah: 'Al-Fatiha',
      score: 85,
      date: '2 hours ago',
    },
    {
      id: 2,
      type: 'Translation',
      surah: 'Al-Ikhlas',
      score: 92,
      date: 'Yesterday',
    },
    {
      id: 3,
      type: 'Tajwid',
      surah: 'An-Nas',
      score: 78,
      date: '2 days ago',
    },
  ];

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <LinearGradient
        colors={['#667eea', '#764ba2']}
        style={styles.header}
      >
        <Text style={styles.headerTitle}>Practice</Text>
        <Text style={styles.headerSubtitle}>Perfect your skills</Text>
      </LinearGradient>

      {/* Practice Types */}
      <View style={styles.practiceContainer}>
        <ThemedText type="subtitle" style={styles.sectionTitle}>
          Choose Practice Type
        </ThemedText>
        
        {practiceTypes.map((type) => (
          <TouchableOpacity 
            key={type.id} 
            style={styles.practiceCard}
            onPress={() => setSelectedMode(type.id)}
          >
            <LinearGradient
              colors={[type.color, `${type.color}CC`]}
              style={styles.practiceGradient}
            >
              <View style={styles.practiceContent}>
                <Text style={styles.practiceIcon}>{type.icon}</Text>
                <View style={styles.practiceInfo}>
                  <Text style={styles.practiceTitle}>{type.title}</Text>
                  <Text style={styles.practiceDescription}>{type.description}</Text>
                  <View style={styles.difficultyBadge}>
                    <Text style={styles.difficultyText}>{type.difficulty}</Text>
                  </View>
                </View>
                <View style={styles.startButton}>
                  <Text style={styles.startButtonText}>Start</Text>
                </View>
              </View>
            </LinearGradient>
          </TouchableOpacity>
        ))}
      </View>

      {/* Recent Sessions */}
      <ThemedView style={styles.recentSection}>
        <ThemedText type="subtitle" style={styles.sectionTitle}>
          Recent Sessions
        </ThemedText>
        
        {recentSessions.map((session) => (
          <View key={session.id} style={styles.sessionCard}>
            <View style={styles.sessionInfo}>
              <Text style={styles.sessionType}>{session.type}</Text>
              <Text style={styles.sessionSurah}>{session.surah}</Text>
              <Text style={styles.sessionDate}>{session.date}</Text>
            </View>
            <View style={styles.scoreContainer}>
              <Text style={styles.scoreText}>{session.score}%</Text>
              <View style={[
                styles.scoreBadge, 
                { backgroundColor: session.score >= 80 ? '#4CAF50' : session.score >= 60 ? '#FF9800' : '#F44336' }
              ]}>
                <Text style={styles.scoreBadgeText}>
                  {session.score >= 80 ? 'Great' : session.score >= 60 ? 'Good' : 'Practice'}
                </Text>
              </View>
            </View>
          </View>
        ))}
      </ThemedView>

      {/* Quick Actions */}
      <ThemedView style={styles.quickActionsSection}>
        <ThemedText type="subtitle" style={styles.sectionTitle}>
          Quick Actions
        </ThemedText>
        
        <View style={styles.actionGrid}>
          <TouchableOpacity style={styles.actionCard}>
            <Text style={styles.actionIcon}>🎯</Text>
            <Text style={styles.actionTitle}>Daily Challenge</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionCard}>
            <Text style={styles.actionIcon}>📊</Text>
            <Text style={styles.actionTitle}>Progress Report</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionCard}>
            <Text style={styles.actionIcon}>🔄</Text>
            <Text style={styles.actionTitle}>Retry Failed</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionCard}>
            <Text style={styles.actionIcon}>⭐</Text>
            <Text style={styles.actionTitle}>Favorites</Text>
          </TouchableOpacity>
        </View>
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    paddingTop: 60,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
  },
  practiceContainer: {
    padding: 20,
  },
  sectionTitle: {
    marginBottom: 15,
    fontSize: 20,
    fontWeight: '600',
  },
  practiceCard: {
    marginBottom: 15,
    borderRadius: 15,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  practiceGradient: {
    padding: 20,
  },
  practiceContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  practiceIcon: {
    fontSize: 32,
    marginRight: 15,
  },
  practiceInfo: {
    flex: 1,
  },
  practiceTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 4,
  },
  practiceDescription: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    marginBottom: 8,
  },
  difficultyBadge: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    alignSelf: 'flex-start',
  },
  difficultyText: {
    fontSize: 12,
    color: 'white',
    fontWeight: '600',
  },
  startButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 20,
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  startButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  recentSection: {
    margin: 20,
    padding: 20,
    borderRadius: 15,
  },
  sessionCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 15,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sessionInfo: {
    flex: 1,
  },
  sessionType: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  sessionSurah: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  sessionDate: {
    fontSize: 12,
    color: '#999',
    marginTop: 4,
  },
  scoreContainer: {
    alignItems: 'center',
  },
  scoreText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  scoreBadge: {
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 2,
  },
  scoreBadgeText: {
    fontSize: 10,
    color: 'white',
    fontWeight: '600',
  },
  quickActionsSection: {
    margin: 20,
    padding: 20,
    borderRadius: 15,
  },
  actionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionCard: {
    width: (width - 80) / 2,
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  actionIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  actionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
  },
});
