import React, { useState } from 'react';
import { StyleSheet, ScrollView, View, Text, TouchableOpacity, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';

const { width } = Dimensions.get('window');

export default function CommunityScreen() {
  const [activeTab, setActiveTab] = useState('leaderboard');

  const leaderboardData = [
    { rank: 1, name: '<PERSON>', xp: 15420, streak: 45, avatar: '👨‍🎓' },
    { rank: 2, name: '<PERSON><PERSON>', xp: 14890, streak: 38, avatar: '👩‍🎓' },
    { rank: 3, name: '<PERSON>', xp: 13750, streak: 32, avatar: '👨‍💼' },
    { rank: 4, name: '<PERSON><PERSON>', xp: 12980, streak: 28, avatar: '👩‍💼' },
    { rank: 5, name: '<PERSON>', xp: 8500, streak: 15, avatar: '👤' },
  ];

  const studyGroups = [
    {
      id: 1,
      name: '<PERSON><PERSON><PERSON><PERSON>',
      members: 24,
      description: 'Perfect your recitation',
      activity: 'Active now',
      color: '#FF6B6B',
    },
    {
      id: 2,
      name: 'Beginners Circle',
      members: 156,
      description: 'Start your journey together',
      activity: '5 min ago',
      color: '#4ECDC4',
    },
    {
      id: 3,
      name: 'Arabic Vocabulary',
      members: 89,
      description: 'Expand your Arabic knowledge',
      activity: '1 hour ago',
      color: '#45B7D1',
    },
  ];

  const recentActivities = [
    {
      id: 1,
      user: 'Ahmed Ali',
      action: 'completed Surah Al-Fatiha',
      time: '2 min ago',
      xp: 150,
    },
    {
      id: 2,
      user: 'Fatima Hassan',
      action: 'achieved 30-day streak',
      time: '15 min ago',
      xp: 500,
    },
    {
      id: 3,
      user: 'Omar Khalil',
      action: 'mastered Tajwid lesson',
      time: '1 hour ago',
      xp: 200,
    },
  ];

  const renderLeaderboard = () => (
    <View style={styles.leaderboardContainer}>
      {leaderboardData.map((user, index) => (
        <View key={user.rank} style={[
          styles.leaderboardItem,
          user.name === 'You' && styles.currentUserItem
        ]}>
          <View style={styles.rankContainer}>
            <Text style={styles.rankText}>#{user.rank}</Text>
            {user.rank <= 3 && (
              <Text style={styles.medal}>
                {user.rank === 1 ? '🥇' : user.rank === 2 ? '🥈' : '🥉'}
              </Text>
            )}
          </View>
          
          <Text style={styles.avatar}>{user.avatar}</Text>
          
          <View style={styles.userInfo}>
            <Text style={[styles.userName, user.name === 'You' && styles.currentUserName]}>
              {user.name}
            </Text>
            <Text style={styles.userStats}>{user.xp.toLocaleString()} XP • {user.streak} day streak</Text>
          </View>
          
          <View style={styles.streakBadge}>
            <Text style={styles.streakText}>🔥</Text>
          </View>
        </View>
      ))}
    </View>
  );

  const renderStudyGroups = () => (
    <View style={styles.groupsContainer}>
      {studyGroups.map((group) => (
        <TouchableOpacity key={group.id} style={styles.groupCard}>
          <LinearGradient
            colors={[group.color, `${group.color}CC`]}
            style={styles.groupGradient}
          >
            <View style={styles.groupHeader}>
              <Text style={styles.groupName}>{group.name}</Text>
              <Text style={styles.groupMembers}>{group.members} members</Text>
            </View>
            <Text style={styles.groupDescription}>{group.description}</Text>
            <View style={styles.groupFooter}>
              <Text style={styles.groupActivity}>{group.activity}</Text>
              <TouchableOpacity style={styles.joinButton}>
                <Text style={styles.joinButtonText}>Join</Text>
              </TouchableOpacity>
            </View>
          </LinearGradient>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderActivities = () => (
    <View style={styles.activitiesContainer}>
      {recentActivities.map((activity) => (
        <View key={activity.id} style={styles.activityItem}>
          <View style={styles.activityContent}>
            <Text style={styles.activityText}>
              <Text style={styles.activityUser}>{activity.user}</Text> {activity.action}
            </Text>
            <Text style={styles.activityTime}>{activity.time}</Text>
          </View>
          <View style={styles.xpBadge}>
            <Text style={styles.xpBadgeText}>+{activity.xp} XP</Text>
          </View>
        </View>
      ))}
    </View>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <LinearGradient
        colors={['#667eea', '#764ba2']}
        style={styles.header}
      >
        <Text style={styles.headerTitle}>Community</Text>
        <Text style={styles.headerSubtitle}>Learn together, grow together</Text>
      </LinearGradient>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <TouchableOpacity 
          style={[styles.tab, activeTab === 'leaderboard' && styles.activeTab]}
          onPress={() => setActiveTab('leaderboard')}
        >
          <Text style={[styles.tabText, activeTab === 'leaderboard' && styles.activeTabText]}>
            Leaderboard
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.tab, activeTab === 'groups' && styles.activeTab]}
          onPress={() => setActiveTab('groups')}
        >
          <Text style={[styles.tabText, activeTab === 'groups' && styles.activeTabText]}>
            Study Groups
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.tab, activeTab === 'activity' && styles.activeTab]}
          onPress={() => setActiveTab('activity')}
        >
          <Text style={[styles.tabText, activeTab === 'activity' && styles.activeTabText]}>
            Activity
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <View style={styles.content}>
        {activeTab === 'leaderboard' && renderLeaderboard()}
        {activeTab === 'groups' && renderStudyGroups()}
        {activeTab === 'activity' && renderActivities()}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    paddingTop: 60,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    marginHorizontal: 20,
    marginTop: -15,
    borderRadius: 15,
    padding: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 10,
  },
  activeTab: {
    backgroundColor: '#667eea',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
  },
  activeTabText: {
    color: 'white',
  },
  content: {
    padding: 20,
  },
  leaderboardContainer: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 15,
  },
  leaderboardItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  currentUserItem: {
    backgroundColor: '#f8f9ff',
    borderRadius: 8,
    marginHorizontal: -8,
    paddingHorizontal: 8,
  },
  rankContainer: {
    width: 40,
    alignItems: 'center',
  },
  rankText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  medal: {
    fontSize: 12,
    marginTop: 2,
  },
  avatar: {
    fontSize: 24,
    marginHorizontal: 12,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  currentUserName: {
    color: '#667eea',
  },
  userStats: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  streakBadge: {
    marginLeft: 10,
  },
  streakText: {
    fontSize: 16,
  },
  groupsContainer: {
    gap: 15,
  },
  groupCard: {
    borderRadius: 15,
    overflow: 'hidden',
  },
  groupGradient: {
    padding: 20,
  },
  groupHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  groupName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
  },
  groupMembers: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.9)',
  },
  groupDescription: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    marginBottom: 15,
  },
  groupFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  groupActivity: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  joinButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 15,
    paddingHorizontal: 15,
    paddingVertical: 6,
  },
  joinButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 12,
  },
  activitiesContainer: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 15,
  },
  activityItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  activityContent: {
    flex: 1,
  },
  activityText: {
    fontSize: 14,
    color: '#333',
  },
  activityUser: {
    fontWeight: '600',
    color: '#667eea',
  },
  activityTime: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  xpBadge: {
    backgroundColor: '#4CAF50',
    borderRadius: 10,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  xpBadgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
  },
});
