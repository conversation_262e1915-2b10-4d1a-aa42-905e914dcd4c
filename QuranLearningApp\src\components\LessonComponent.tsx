import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, Dimensions, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Audio } from 'expo-av';
import { useAppDispatch, useAppSelector } from '../store';
import { nextExercise, completeLesson } from '../store/slices/lessonSlice';
import { updateUserXP } from '../store/slices/userSlice';
import { Exercise } from '../types';

const { width } = Dimensions.get('window');

interface LessonComponentProps {
  onComplete: () => void;
  onExit: () => void;
}

export default function LessonComponent({ onComplete, onExit }: LessonComponentProps) {
  const dispatch = useAppDispatch();
  const { currentLesson, currentExercise, exerciseIndex, lessonProgress } = useAppSelector(
    state => state.lessons
  );
  
  const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null);
  const [showResult, setShowResult] = useState(false);
  const [isCorrect, setIsCorrect] = useState(false);
  const [sound, setSound] = useState<Audio.Sound | null>(null);

  useEffect(() => {
    return sound
      ? () => {
          sound.unloadAsync();
        }
      : undefined;
  }, [sound]);

  const playAudio = async (audioUrl?: string) => {
    if (!audioUrl) return;
    
    try {
      const { sound } = await Audio.Sound.createAsync({ uri: audioUrl });
      setSound(sound);
      await sound.playAsync();
    } catch (error) {
      console.log('Error playing audio:', error);
    }
  };

  const handleAnswerSelect = (answer: string) => {
    if (showResult) return;
    setSelectedAnswer(answer);
  };

  const handleSubmit = () => {
    if (!selectedAnswer || !currentExercise) return;

    const correct = selectedAnswer === currentExercise.correctAnswer;
    setIsCorrect(correct);
    setShowResult(true);

    if (correct) {
      // Award XP for correct answer
      dispatch(updateUserXP(25));
    }
  };

  const handleNext = () => {
    if (!currentLesson) return;

    setSelectedAnswer(null);
    setShowResult(false);

    if (exerciseIndex < currentLesson.content.exercises.length - 1) {
      dispatch(nextExercise());
    } else {
      // Lesson completed
      dispatch(completeLesson());
      dispatch(updateUserXP(currentLesson.xpReward));
      onComplete();
    }
  };

  const renderMultipleChoice = (exercise: Exercise) => (
    <View style={styles.exerciseContainer}>
      {/* Question */}
      <View style={styles.questionContainer}>
        <Text style={styles.questionText}>{exercise.question}</Text>
        {exercise.arabicText && (
          <View style={styles.arabicContainer}>
            <Text style={styles.arabicText}>{exercise.arabicText}</Text>
            {exercise.audioUrl && (
              <TouchableOpacity
                style={styles.audioButton}
                onPress={() => playAudio(exercise.audioUrl)}
              >
                <Text style={styles.audioIcon}>🔊</Text>
              </TouchableOpacity>
            )}
          </View>
        )}
      </View>

      {/* Options */}
      <View style={styles.optionsContainer}>
        {exercise.options?.map((option, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.optionButton,
              selectedAnswer === option && styles.selectedOption,
              showResult && option === exercise.correctAnswer && styles.correctOption,
              showResult && selectedAnswer === option && option !== exercise.correctAnswer && styles.incorrectOption,
            ]}
            onPress={() => handleAnswerSelect(option)}
            disabled={showResult}
          >
            <Text style={[
              styles.optionText,
              selectedAnswer === option && styles.selectedOptionText,
              showResult && option === exercise.correctAnswer && styles.correctOptionText,
            ]}>
              {option}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Result */}
      {showResult && (
        <View style={styles.resultContainer}>
          <View style={[
            styles.resultCard,
            isCorrect ? styles.correctResult : styles.incorrectResult,
          ]}>
            <Text style={styles.resultIcon}>
              {isCorrect ? '✅' : '❌'}
            </Text>
            <Text style={styles.resultText}>
              {isCorrect ? 'Correct!' : 'Incorrect'}
            </Text>
            {exercise.explanation && (
              <Text style={styles.explanationText}>
                {exercise.explanation}
              </Text>
            )}
          </View>
        </View>
      )}
    </View>
  );

  const renderRecitation = (exercise: Exercise) => (
    <View style={styles.exerciseContainer}>
      <View style={styles.questionContainer}>
        <Text style={styles.questionText}>Practice reciting this verse</Text>
        {exercise.arabicText && (
          <View style={styles.arabicContainer}>
            <Text style={styles.arabicText}>{exercise.arabicText}</Text>
            <TouchableOpacity
              style={styles.audioButton}
              onPress={() => playAudio(exercise.audioUrl)}
            >
              <Text style={styles.audioIcon}>🔊</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      <View style={styles.recitationContainer}>
        <TouchableOpacity style={styles.recordButton}>
          <Text style={styles.recordIcon}>🎤</Text>
          <Text style={styles.recordText}>Tap to Record</Text>
        </TouchableOpacity>
        
        <Text style={styles.recitationHint}>
          Listen to the audio first, then record your recitation
        </Text>
      </View>
    </View>
  );

  if (!currentLesson || !currentExercise) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>No lesson available</Text>
      </View>
    );
  }

  return (
    <LinearGradient colors={['#667eea', '#764ba2']} style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.exitButton} onPress={onExit}>
          <Text style={styles.exitIcon}>✕</Text>
        </TouchableOpacity>
        
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View style={[styles.progressFill, { width: `${lessonProgress}%` }]} />
          </View>
          <Text style={styles.progressText}>
            {exerciseIndex + 1} / {currentLesson.content.exercises.length}
          </Text>
        </View>
        
        <View style={styles.xpContainer}>
          <Text style={styles.xpText}>+{currentLesson.xpReward} XP</Text>
        </View>
      </View>

      {/* Content */}
      <View style={styles.content}>
        <View style={styles.lessonCard}>
          {currentExercise.type === 'multiple_choice' && renderMultipleChoice(currentExercise)}
          {currentExercise.type === 'recitation' && renderRecitation(currentExercise)}
        </View>
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        {!showResult ? (
          <TouchableOpacity
            style={[styles.submitButton, !selectedAnswer && styles.disabledButton]}
            onPress={handleSubmit}
            disabled={!selectedAnswer}
          >
            <Text style={styles.submitButtonText}>Submit</Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity style={styles.nextButton} onPress={handleNext}>
            <Text style={styles.nextButtonText}>
              {exerciseIndex < currentLesson.content.exercises.length - 1 ? 'Next' : 'Complete'}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 50,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  exitButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  exitIcon: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  progressContainer: {
    flex: 1,
    marginHorizontal: 20,
  },
  progressBar: {
    height: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: 'white',
    borderRadius: 4,
  },
  progressText: {
    color: 'white',
    fontSize: 12,
    textAlign: 'center',
    marginTop: 5,
  },
  xpContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 15,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  xpText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  lessonCard: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 20,
    flex: 1,
  },
  exerciseContainer: {
    flex: 1,
  },
  questionContainer: {
    marginBottom: 30,
  },
  questionText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
    marginBottom: 20,
  },
  arabicContainer: {
    alignItems: 'center',
    backgroundColor: '#f8f9ff',
    borderRadius: 15,
    padding: 20,
  },
  arabicText: {
    fontSize: 32,
    color: '#333',
    textAlign: 'center',
    marginBottom: 15,
    fontFamily: 'Amiri-Regular',
  },
  audioButton: {
    backgroundColor: '#667eea',
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  audioIcon: {
    fontSize: 20,
  },
  optionsContainer: {
    gap: 15,
  },
  optionButton: {
    backgroundColor: '#f5f5f5',
    borderRadius: 15,
    padding: 20,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedOption: {
    backgroundColor: '#e3f2fd',
    borderColor: '#2196F3',
  },
  correctOption: {
    backgroundColor: '#e8f5e8',
    borderColor: '#4CAF50',
  },
  incorrectOption: {
    backgroundColor: '#ffebee',
    borderColor: '#f44336',
  },
  optionText: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
    fontWeight: '500',
  },
  selectedOptionText: {
    color: '#2196F3',
    fontWeight: '600',
  },
  correctOptionText: {
    color: '#4CAF50',
    fontWeight: '600',
  },
  resultContainer: {
    marginTop: 20,
  },
  resultCard: {
    borderRadius: 15,
    padding: 20,
    alignItems: 'center',
  },
  correctResult: {
    backgroundColor: '#e8f5e8',
  },
  incorrectResult: {
    backgroundColor: '#ffebee',
  },
  resultIcon: {
    fontSize: 32,
    marginBottom: 10,
  },
  resultText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  explanationText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  recitationContainer: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  recordButton: {
    backgroundColor: '#FF6B6B',
    borderRadius: 50,
    width: 100,
    height: 100,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  recordIcon: {
    fontSize: 32,
    marginBottom: 5,
  },
  recordText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  recitationHint: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  footer: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  submitButton: {
    backgroundColor: 'white',
    borderRadius: 25,
    paddingVertical: 18,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
  },
  submitButtonText: {
    color: '#667eea',
    fontSize: 18,
    fontWeight: 'bold',
  },
  nextButton: {
    backgroundColor: '#4CAF50',
    borderRadius: 25,
    paddingVertical: 18,
    alignItems: 'center',
  },
  nextButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  errorText: {
    color: 'white',
    fontSize: 18,
    textAlign: 'center',
  },
});
