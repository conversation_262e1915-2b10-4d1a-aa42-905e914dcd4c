import { User, SkillTreeNode, Lesson, Achievement, Surah } from '../types';

export const sampleUser: User = {
  id: '1',
  name: '<PERSON>',
  email: '<EMAIL>',
  level: 3,
  xp: 2450,
  streak: 15,
  joinDate: '2024-01-15T00:00:00Z',
  preferences: {
    language: 'en',
    reciterPreference: 'mishary',
    notificationsEnabled: true,
    dailyGoalMinutes: 15,
    arabicFontSize: 'medium',
    translationLanguage: 'en',
    darkMode: false,
  },
  progress: {
    completedLessons: ['lesson_1', 'lesson_2', 'lesson_3'],
    currentSkillTree: [],
    achievements: [],
    weeklyProgress: [],
    totalStudyTime: 1200, // in minutes
    lastStudyDate: '2024-01-30T00:00:00Z',
  },
};

export const sampleSkillTree: SkillTreeNode[] = [
  {
    id: 'arabic_letters',
    title: 'Arabic Letters',
    description: 'Learn the 28 letters of the Arabic alphabet',
    type: 'arabic_letters',
    level: 1,
    isUnlocked: true,
    isCompleted: true,
    prerequisiteIds: [],
    lessons: [],
    xpReward: 500,
    icon: '🔤',
  },
  {
    id: 'basic_tajwid',
    title: 'Basic Tajwid',
    description: 'Fundamental pronunciation rules',
    type: 'tajwid',
    level: 2,
    isUnlocked: true,
    isCompleted: false,
    prerequisiteIds: ['arabic_letters'],
    lessons: [],
    xpReward: 750,
    icon: '🎵',
  },
  {
    id: 'al_fatiha',
    title: 'Surah Al-Fatiha',
    description: 'The Opening chapter of the Quran',
    type: 'surah',
    level: 2,
    isUnlocked: true,
    isCompleted: false,
    prerequisiteIds: ['arabic_letters'],
    lessons: [],
    xpReward: 1000,
    icon: '📖',
  },
  {
    id: 'short_surahs',
    title: 'Short Surahs',
    description: 'Learn the last 10 surahs',
    type: 'surah',
    level: 3,
    isUnlocked: false,
    isCompleted: false,
    prerequisiteIds: ['al_fatiha', 'basic_tajwid'],
    lessons: [],
    xpReward: 1500,
    icon: '📚',
  },
];

export const sampleLessons: Lesson[] = [
  {
    id: 'lesson_1',
    title: 'Arabic Letter Alif',
    description: 'Learn the first letter of Arabic alphabet',
    type: 'reading',
    content: {
      arabicText: 'أ',
      transliteration: 'Alif',
      translation: 'The letter Alif',
      exercises: [
        {
          id: 'ex_1',
          type: 'multiple_choice',
          question: 'What is the name of this letter: أ',
          options: ['Alif', 'Ba', 'Ta', 'Tha'],
          correctAnswer: 'Alif',
          explanation: 'This is the letter Alif, the first letter of the Arabic alphabet.',
          arabicText: 'أ',
        },
      ],
    },
    duration: 5,
    xpReward: 50,
    isCompleted: true,
    difficulty: 'beginner',
  },
  {
    id: 'lesson_2',
    title: 'Arabic Letter Ba',
    description: 'Learn the second letter of Arabic alphabet',
    type: 'reading',
    content: {
      arabicText: 'ب',
      transliteration: 'Ba',
      translation: 'The letter Ba',
      exercises: [
        {
          id: 'ex_2',
          type: 'multiple_choice',
          question: 'What is the name of this letter: ب',
          options: ['Alif', 'Ba', 'Ta', 'Tha'],
          correctAnswer: 'Ba',
          explanation: 'This is the letter Ba, the second letter of the Arabic alphabet.',
          arabicText: 'ب',
        },
      ],
    },
    duration: 5,
    xpReward: 50,
    isCompleted: true,
    difficulty: 'beginner',
  },
];

export const sampleAchievements: Achievement[] = [
  {
    id: 'first_lesson',
    title: 'First Steps',
    description: 'Complete your first lesson',
    icon: '🌟',
    xpReward: 100,
    category: 'completion',
  },
  {
    id: 'week_streak',
    title: 'Week Warrior',
    description: 'Maintain a 7-day learning streak',
    icon: '🔥',
    xpReward: 500,
    category: 'streak',
  },
  {
    id: 'tajwid_master',
    title: 'Tajwid Master',
    description: 'Master 10 Tajwid rules',
    icon: '🎵',
    xpReward: 1000,
    category: 'completion',
  },
  {
    id: 'speed_reader',
    title: 'Speed Reader',
    description: 'Complete a lesson in under 3 minutes',
    icon: '⚡',
    xpReward: 200,
    category: 'speed',
  },
  {
    id: 'perfect_score',
    title: 'Perfect Score',
    description: 'Get 100% accuracy in a lesson',
    icon: '💯',
    xpReward: 300,
    category: 'accuracy',
  },
];

export const sampleSurahs: Surah[] = [
  {
    id: 1,
    name: 'Al-Fatiha',
    arabicName: 'الفاتحة',
    englishName: 'The Opening',
    numberOfAyahs: 7,
    revelationType: 'meccan',
    ayahs: [
      {
        id: 1,
        surahId: 1,
        ayahNumber: 1,
        arabicText: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
        transliteration: 'Bismillahi r-rahmani r-raheem',
        translation: 'In the name of Allah, the Most Gracious, the Most Merciful',
        audioUrl: 'https://example.com/audio/1_1.mp3',
        tajwidMarkup: [],
      },
      {
        id: 2,
        surahId: 1,
        ayahNumber: 2,
        arabicText: 'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ',
        transliteration: 'Alhamdu lillahi rabbi l-alameen',
        translation: 'All praise is due to Allah, Lord of the worlds',
        audioUrl: 'https://example.com/audio/1_2.mp3',
        tajwidMarkup: [],
      },
    ],
  },
  {
    id: 114,
    name: 'An-Nas',
    arabicName: 'الناس',
    englishName: 'Mankind',
    numberOfAyahs: 6,
    revelationType: 'meccan',
    ayahs: [
      {
        id: 1,
        surahId: 114,
        ayahNumber: 1,
        arabicText: 'قُلْ أَعُوذُ بِرَبِّ النَّاسِ',
        transliteration: 'Qul a\'udhu bi rabbi n-nas',
        translation: 'Say: I seek refuge with the Lord of mankind',
        audioUrl: 'https://example.com/audio/114_1.mp3',
        tajwidMarkup: [],
      },
    ],
  },
];

// Utility functions for sample data
export const getSampleUserProgress = () => ({
  todayProgress: 12,
  dailyGoal: 15,
  weeklyStats: [
    {
      week: '2024-W04',
      studyDays: 5,
      totalXp: 450,
      lessonsCompleted: 8,
      averageAccuracy: 92,
    },
  ],
});

export const getRandomQuranVerse = () => {
  const verses = [
    {
      arabic: 'وَمَن يَتَّقِ اللَّهَ يَجْعَل لَّهُ مَخْرَجًا',
      transliteration: 'Wa man yattaqi llaha yaj\'al lahu makhrajan',
      translation: 'And whoever fears Allah - He will make for him a way out',
      reference: 'Quran 65:2',
    },
    {
      arabic: 'إِنَّ مَعَ الْعُسْرِ يُسْرًا',
      transliteration: 'Inna ma\'a l-\'usri yusran',
      translation: 'Indeed, with hardship comes ease',
      reference: 'Quran 94:6',
    },
    {
      arabic: 'وَاللَّهُ غَالِبٌ عَلَىٰ أَمْرِهِ',
      transliteration: 'Wallahu ghalibun \'ala amrihi',
      translation: 'And Allah is predominant over His affair',
      reference: 'Quran 12:21',
    },
  ];
  
  return verses[Math.floor(Math.random() * verses.length)];
};
