import React from "react";
import {
  StyleSheet,
  ScrollView,
  View,
  Text,
  TouchableOpacity,
  Dimensions,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { useRouter } from "expo-router";
import { useAppSelector } from "@/src/store";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";

const { width } = Dimensions.get("window");

export default function LearnScreen() {
  const router = useRouter();
  const skillTree = useAppSelector((state) => state.lessons.skillTree);
  const user = useAppSelector((state) => state.user.currentUser);

  const skillCategories = [
    {
      id: "arabic_letters",
      title: "Arabic Letters",
      icon: "🔤",
      description: "Learn the Arabic alphabet",
      color: "#FF6B6B",
      progress: 85,
    },
    {
      id: "tajwid",
      title: "Tajwid Rules",
      icon: "🎵",
      description: "Master pronunciation rules",
      color: "#4ECDC4",
      progress: 60,
    },
    {
      id: "surah",
      title: "Surahs",
      icon: "📖",
      description: "Memorize Quranic chapters",
      color: "#45B7D1",
      progress: 40,
    },
    {
      id: "vocabulary",
      title: "Vocabulary",
      icon: "📚",
      description: "Build Arabic vocabulary",
      color: "#96CEB4",
      progress: 30,
    },
    {
      id: "grammar",
      title: "Grammar",
      icon: "⚖️",
      description: "Understand Arabic grammar",
      color: "#FFEAA7",
      progress: 20,
    },
    {
      id: "tafsir",
      title: "Tafsir",
      icon: "💡",
      description: "Learn verse meanings",
      color: "#DDA0DD",
      progress: 10,
    },
  ];

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <LinearGradient colors={["#667eea", "#764ba2"]} style={styles.header}>
        <Text style={styles.headerTitle}>Learning Path</Text>
        <Text style={styles.headerSubtitle}>Choose your journey</Text>

        {/* Level Progress */}
        <View style={styles.levelContainer}>
          <Text style={styles.levelText}>Level {user?.level || 1}</Text>
          <View style={styles.xpContainer}>
            <View style={styles.xpBar}>
              <View
                style={[
                  styles.xpFill,
                  { width: `${((user?.xp || 0) % 1000) / 10}%` },
                ]}
              />
            </View>
            <Text style={styles.xpText}>{(user?.xp || 0) % 1000}/1000 XP</Text>
          </View>
        </View>
      </LinearGradient>

      {/* Skill Categories */}
      <View style={styles.skillsContainer}>
        <ThemedText type="subtitle" style={styles.sectionTitle}>
          Skills
        </ThemedText>

        {skillCategories.map((category, index) => (
          <TouchableOpacity
            key={category.id}
            style={styles.skillCard}
            onPress={() => router.push("/skill-tree")}
          >
            <LinearGradient
              colors={[category.color, `${category.color}CC`]}
              style={styles.skillGradient}
            >
              <View style={styles.skillContent}>
                <View style={styles.skillHeader}>
                  <Text style={styles.skillIcon}>{category.icon}</Text>
                  <View style={styles.skillInfo}>
                    <Text style={styles.skillTitle}>{category.title}</Text>
                    <Text style={styles.skillDescription}>
                      {category.description}
                    </Text>
                  </View>
                  <View style={styles.progressCircle}>
                    <Text style={styles.progressText}>
                      {category.progress}%
                    </Text>
                  </View>
                </View>

                {/* Progress Bar */}
                <View style={styles.skillProgressBar}>
                  <View
                    style={[
                      styles.skillProgressFill,
                      { width: `${category.progress}%` },
                    ]}
                  />
                </View>

                {/* Lessons Count */}
                <Text style={styles.lessonsCount}>
                  {Math.floor(category.progress / 10)} lessons completed
                </Text>
              </View>
            </LinearGradient>
          </TouchableOpacity>
        ))}
      </View>

      {/* Quick Practice */}
      <ThemedView style={styles.practiceSection}>
        <ThemedText type="subtitle" style={styles.sectionTitle}>
          Quick Practice
        </ThemedText>

        <View style={styles.practiceGrid}>
          <TouchableOpacity style={styles.practiceCard}>
            <Text style={styles.practiceIcon}>🎯</Text>
            <Text style={styles.practiceTitle}>Daily Challenge</Text>
            <Text style={styles.practiceSubtitle}>5 minutes</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.practiceCard}>
            <Text style={styles.practiceIcon}>⚡</Text>
            <Text style={styles.practiceTitle}>Speed Round</Text>
            <Text style={styles.practiceSubtitle}>Quick quiz</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.practiceCard}>
            <Text style={styles.practiceIcon}>🔄</Text>
            <Text style={styles.practiceTitle}>Review</Text>
            <Text style={styles.practiceSubtitle}>Past lessons</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.practiceCard}>
            <Text style={styles.practiceIcon}>🎲</Text>
            <Text style={styles.practiceTitle}>Random</Text>
            <Text style={styles.practiceSubtitle}>Surprise me</Text>
          </TouchableOpacity>
        </View>
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  header: {
    paddingTop: 60,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: "bold",
    color: "white",
    textAlign: "center",
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: "rgba(255, 255, 255, 0.9)",
    textAlign: "center",
    marginBottom: 20,
  },
  levelContainer: {
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: 15,
    padding: 15,
  },
  levelText: {
    fontSize: 18,
    fontWeight: "bold",
    color: "white",
    textAlign: "center",
    marginBottom: 10,
  },
  xpContainer: {
    alignItems: "center",
  },
  xpBar: {
    width: "100%",
    height: 8,
    backgroundColor: "rgba(255, 255, 255, 0.3)",
    borderRadius: 4,
    overflow: "hidden",
  },
  xpFill: {
    height: "100%",
    backgroundColor: "white",
    borderRadius: 4,
  },
  xpText: {
    fontSize: 12,
    color: "white",
    marginTop: 5,
  },
  skillsContainer: {
    padding: 20,
  },
  sectionTitle: {
    marginBottom: 15,
    fontSize: 20,
    fontWeight: "600",
  },
  skillCard: {
    marginBottom: 15,
    borderRadius: 15,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  skillGradient: {
    padding: 20,
  },
  skillContent: {
    flex: 1,
  },
  skillHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 15,
  },
  skillIcon: {
    fontSize: 32,
    marginRight: 15,
  },
  skillInfo: {
    flex: 1,
  },
  skillTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "white",
    marginBottom: 4,
  },
  skillDescription: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.9)",
  },
  progressCircle: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: "rgba(255, 255, 255, 0.3)",
    justifyContent: "center",
    alignItems: "center",
  },
  progressText: {
    fontSize: 12,
    fontWeight: "bold",
    color: "white",
  },
  skillProgressBar: {
    height: 6,
    backgroundColor: "rgba(255, 255, 255, 0.3)",
    borderRadius: 3,
    overflow: "hidden",
    marginBottom: 10,
  },
  skillProgressFill: {
    height: "100%",
    backgroundColor: "white",
    borderRadius: 3,
  },
  lessonsCount: {
    fontSize: 12,
    color: "rgba(255, 255, 255, 0.9)",
  },
  practiceSection: {
    margin: 20,
    padding: 20,
    borderRadius: 15,
  },
  practiceGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  practiceCard: {
    width: (width - 80) / 2,
    backgroundColor: "white",
    borderRadius: 12,
    padding: 20,
    alignItems: "center",
    marginBottom: 15,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  practiceIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  practiceTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 4,
  },
  practiceSubtitle: {
    fontSize: 12,
    color: "#666",
  },
});
