import { configureStore } from '@reduxjs/toolkit';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';
import userSlice from './slices/userSlice';
import lessonSlice from './slices/lessonSlice';
import progressSlice from './slices/progressSlice';
import achievementSlice from './slices/achievementSlice';
import communitySlice from './slices/communitySlice';
import settingsSlice from './slices/settingsSlice';

export const store = configureStore({
  reducer: {
    user: userSlice,
    lessons: lessonSlice,
    progress: progressSlice,
    achievements: achievementSlice,
    community: communitySlice,
    settings: settingsSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
