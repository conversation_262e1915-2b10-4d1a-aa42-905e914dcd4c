import React, { useState } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, Dimensions, ScrollView } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { useAppDispatch } from '@/src/store';
import { setUser } from '@/src/store/slices/userSlice';
import { sampleUser } from '@/src/data/sampleData';

const { width, height } = Dimensions.get('window');

const onboardingSteps = [
  {
    id: 1,
    title: 'Welcome to Quran Learning',
    subtitle: 'Your journey to understanding the Quran begins here',
    description: 'Learn Arabic, master <PERSON><PERSON><PERSON><PERSON>, and deepen your connection with the Holy Quran through interactive lessons and gamified learning.',
    icon: '📖',
    color: '#4CAF50',
  },
  {
    id: 2,
    title: 'Interactive Learning',
    subtitle: 'Engage with every lesson',
    description: 'Practice pronunciation, test your knowledge with quizzes, and track your progress as you advance through skill trees.',
    icon: '🎯',
    color: '#2196F3',
  },
  {
    id: 3,
    title: 'Community & Growth',
    subtitle: 'Learn together, grow together',
    description: 'Join study groups, compete on leaderboards, and celebrate achievements with fellow learners worldwide.',
    icon: '👥',
    color: '#FF9800',
  },
  {
    id: 4,
    title: 'Personalized Experience',
    subtitle: 'Tailored to your pace',
    description: 'Set your daily goals, choose your preferred reciter, and customize your learning experience.',
    icon: '⚙️',
    color: '#9C27B0',
  },
];

export default function OnboardingScreen() {
  const [currentStep, setCurrentStep] = useState(0);
  const [userInfo, setUserInfo] = useState({
    name: '',
    level: 'beginner',
    dailyGoal: 15,
    interests: [] as string[],
  });
  
  const router = useRouter();
  const dispatch = useAppDispatch();

  const handleNext = () => {
    if (currentStep < onboardingSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // Complete onboarding
      completeOnboarding();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const completeOnboarding = () => {
    // Set user data and navigate to main app
    const newUser = {
      ...sampleUser,
      name: userInfo.name || 'Student',
      preferences: {
        ...sampleUser.preferences,
        dailyGoalMinutes: userInfo.dailyGoal,
      },
    };
    
    dispatch(setUser(newUser));
    router.replace('/(tabs)');
  };

  const renderWelcomeSteps = () => {
    const step = onboardingSteps[currentStep];
    
    return (
      <LinearGradient
        colors={[step.color, `${step.color}CC`]}
        style={styles.stepContainer}
      >
        <View style={styles.stepContent}>
          <Text style={styles.stepIcon}>{step.icon}</Text>
          <Text style={styles.stepTitle}>{step.title}</Text>
          <Text style={styles.stepSubtitle}>{step.subtitle}</Text>
          <Text style={styles.stepDescription}>{step.description}</Text>
        </View>
        
        {/* Progress Dots */}
        <View style={styles.progressContainer}>
          {onboardingSteps.map((_, index) => (
            <View
              key={index}
              style={[
                styles.progressDot,
                index === currentStep && styles.activeDot,
              ]}
            />
          ))}
        </View>
        
        {/* Navigation Buttons */}
        <View style={styles.buttonContainer}>
          {currentStep > 0 && (
            <TouchableOpacity style={styles.secondaryButton} onPress={handlePrevious}>
              <Text style={styles.secondaryButtonText}>Previous</Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity style={styles.primaryButton} onPress={handleNext}>
            <Text style={styles.primaryButtonText}>
              {currentStep === onboardingSteps.length - 1 ? 'Get Started' : 'Next'}
            </Text>
          </TouchableOpacity>
        </View>
      </LinearGradient>
    );
  };

  const renderUserSetup = () => {
    return (
      <ScrollView style={styles.setupContainer}>
        <View style={styles.setupContent}>
          <Text style={styles.setupTitle}>Let's personalize your experience</Text>
          
          {/* Name Input */}
          <View style={styles.inputSection}>
            <Text style={styles.inputLabel}>What should we call you?</Text>
            <TouchableOpacity style={styles.input}>
              <Text style={styles.inputText}>
                {userInfo.name || 'Enter your name'}
              </Text>
            </TouchableOpacity>
          </View>
          
          {/* Experience Level */}
          <View style={styles.inputSection}>
            <Text style={styles.inputLabel}>Your experience level</Text>
            <View style={styles.optionGrid}>
              {['beginner', 'intermediate', 'advanced'].map((level) => (
                <TouchableOpacity
                  key={level}
                  style={[
                    styles.optionCard,
                    userInfo.level === level && styles.selectedOption,
                  ]}
                  onPress={() => setUserInfo({ ...userInfo, level })}
                >
                  <Text style={[
                    styles.optionText,
                    userInfo.level === level && styles.selectedOptionText,
                  ]}>
                    {level.charAt(0).toUpperCase() + level.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
          
          {/* Daily Goal */}
          <View style={styles.inputSection}>
            <Text style={styles.inputLabel}>Daily learning goal</Text>
            <View style={styles.optionGrid}>
              {[5, 10, 15, 20, 30].map((minutes) => (
                <TouchableOpacity
                  key={minutes}
                  style={[
                    styles.optionCard,
                    userInfo.dailyGoal === minutes && styles.selectedOption,
                  ]}
                  onPress={() => setUserInfo({ ...userInfo, dailyGoal: minutes })}
                >
                  <Text style={[
                    styles.optionText,
                    userInfo.dailyGoal === minutes && styles.selectedOptionText,
                  ]}>
                    {minutes} min
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
          
          {/* Interests */}
          <View style={styles.inputSection}>
            <Text style={styles.inputLabel}>What interests you most?</Text>
            <View style={styles.interestGrid}>
              {[
                { id: 'recitation', label: 'Recitation', icon: '🎤' },
                { id: 'translation', label: 'Translation', icon: '📝' },
                { id: 'tajwid', label: 'Tajwid', icon: '🎵' },
                { id: 'memorization', label: 'Memorization', icon: '🧠' },
                { id: 'arabic', label: 'Arabic Language', icon: '🔤' },
                { id: 'tafsir', label: 'Tafsir', icon: '💡' },
              ].map((interest) => (
                <TouchableOpacity
                  key={interest.id}
                  style={[
                    styles.interestCard,
                    userInfo.interests.includes(interest.id) && styles.selectedInterest,
                  ]}
                  onPress={() => {
                    const newInterests = userInfo.interests.includes(interest.id)
                      ? userInfo.interests.filter(i => i !== interest.id)
                      : [...userInfo.interests, interest.id];
                    setUserInfo({ ...userInfo, interests: newInterests });
                  }}
                >
                  <Text style={styles.interestIcon}>{interest.icon}</Text>
                  <Text style={[
                    styles.interestText,
                    userInfo.interests.includes(interest.id) && styles.selectedInterestText,
                  ]}>
                    {interest.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
          
          <TouchableOpacity style={styles.completeButton} onPress={completeOnboarding}>
            <Text style={styles.completeButtonText}>Start Learning</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    );
  };

  return (
    <View style={styles.container}>
      {currentStep < onboardingSteps.length ? renderWelcomeSteps() : renderUserSetup()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  stepContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 30,
  },
  stepContent: {
    alignItems: 'center',
    marginBottom: 50,
  },
  stepIcon: {
    fontSize: 80,
    marginBottom: 30,
  },
  stepTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 10,
  },
  stepSubtitle: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    marginBottom: 20,
  },
  stepDescription: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    lineHeight: 24,
  },
  progressContainer: {
    flexDirection: 'row',
    marginBottom: 40,
  },
  progressDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    marginHorizontal: 5,
  },
  activeDot: {
    backgroundColor: 'white',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  primaryButton: {
    backgroundColor: 'white',
    borderRadius: 25,
    paddingVertical: 15,
    paddingHorizontal: 30,
    flex: 1,
    marginLeft: 10,
  },
  primaryButtonText: {
    color: '#333',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  secondaryButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 25,
    paddingVertical: 15,
    paddingHorizontal: 30,
    flex: 1,
    marginRight: 10,
  },
  secondaryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  setupContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  setupContent: {
    padding: 30,
    paddingTop: 60,
  },
  setupTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 40,
  },
  inputSection: {
    marginBottom: 30,
  },
  inputLabel: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 15,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 12,
    padding: 15,
    backgroundColor: '#f9f9f9',
  },
  inputText: {
    fontSize: 16,
    color: '#666',
  },
  optionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
  },
  optionCard: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 12,
    padding: 15,
    backgroundColor: '#f9f9f9',
    minWidth: 80,
    alignItems: 'center',
  },
  selectedOption: {
    backgroundColor: '#4CAF50',
    borderColor: '#4CAF50',
  },
  optionText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '600',
  },
  selectedOptionText: {
    color: 'white',
  },
  interestGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
  },
  interestCard: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 12,
    padding: 15,
    backgroundColor: '#f9f9f9',
    width: (width - 80) / 2,
    alignItems: 'center',
  },
  selectedInterest: {
    backgroundColor: '#4CAF50',
    borderColor: '#4CAF50',
  },
  interestIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  interestText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '600',
    textAlign: 'center',
  },
  selectedInterestText: {
    color: 'white',
  },
  completeButton: {
    backgroundColor: '#4CAF50',
    borderRadius: 25,
    paddingVertical: 18,
    marginTop: 30,
  },
  completeButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});
