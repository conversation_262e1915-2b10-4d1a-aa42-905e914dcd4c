// Core Types for Quran Learning App

export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  level: number;
  xp: number;
  streak: number;
  joinDate: string;
  preferences: UserPreferences;
  progress: UserProgress;
}

export interface UserPreferences {
  language: string;
  reciterPreference: string;
  notificationsEnabled: boolean;
  dailyGoalMinutes: number;
  arabicFontSize: 'small' | 'medium' | 'large';
  translationLanguage: string;
  darkMode: boolean;
}

export interface UserProgress {
  completedLessons: string[];
  currentSkillTree: SkillTreeNode[];
  achievements: Achievement[];
  weeklyProgress: WeeklyProgress[];
  totalStudyTime: number;
  lastStudyDate: string;
}

export interface SkillTreeNode {
  id: string;
  title: string;
  description: string;
  type: 'arabic_letters' | 'tajwid' | 'surah' | 'vocabulary' | 'grammar' | 'tafsir';
  level: number;
  isUnlocked: boolean;
  isCompleted: boolean;
  prerequisiteIds: string[];
  lessons: Lesson[];
  xpReward: number;
  icon: string;
}

export interface Lesson {
  id: string;
  title: string;
  description: string;
  type: 'listening' | 'reading' | 'recitation' | 'translation' | 'tajwid' | 'quiz';
  content: LessonContent;
  duration: number; // in minutes
  xpReward: number;
  isCompleted: boolean;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
}

export interface LessonContent {
  arabicText?: string;
  transliteration?: string;
  translation?: string;
  audioUrl?: string;
  exercises: Exercise[];
  tajwidRules?: TajwidRule[];
  vocabulary?: VocabularyItem[];
}

export interface Exercise {
  id: string;
  type: 'multiple_choice' | 'fill_blank' | 'audio_match' | 'recitation' | 'drag_drop';
  question: string;
  options?: string[];
  correctAnswer: string | string[];
  explanation?: string;
  audioUrl?: string;
  arabicText?: string;
}

export interface TajwidRule {
  id: string;
  name: string;
  arabicName: string;
  description: string;
  examples: string[];
  audioExamples: string[];
  color: string;
}

export interface VocabularyItem {
  id: string;
  arabic: string;
  transliteration: string;
  translation: string;
  audioUrl: string;
  examples: string[];
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  xpReward: number;
  unlockedAt?: string;
  category: 'streak' | 'completion' | 'recitation' | 'speed' | 'accuracy';
}

export interface WeeklyProgress {
  week: string;
  studyDays: number;
  totalXp: number;
  lessonsCompleted: number;
  averageAccuracy: number;
}

export interface Surah {
  id: number;
  name: string;
  arabicName: string;
  englishName: string;
  numberOfAyahs: number;
  revelationType: 'meccan' | 'medinan';
  ayahs: Ayah[];
}

export interface Ayah {
  id: number;
  surahId: number;
  ayahNumber: number;
  arabicText: string;
  transliteration: string;
  translation: string;
  audioUrl: string;
  tajwidMarkup: TajwidMarkup[];
}

export interface TajwidMarkup {
  start: number;
  end: number;
  rule: string;
  color: string;
}

export interface RecitationSession {
  id: string;
  userId: string;
  ayahId: number;
  recordingUrl: string;
  accuracy: number;
  feedback: string[];
  timestamp: string;
  duration: number;
}

export interface StudyGroup {
  id: string;
  name: string;
  description: string;
  memberCount: number;
  isPrivate: boolean;
  adminId: string;
  members: GroupMember[];
  activities: GroupActivity[];
}

export interface GroupMember {
  userId: string;
  name: string;
  avatar?: string;
  role: 'admin' | 'member';
  joinDate: string;
  totalXp: number;
}

export interface GroupActivity {
  id: string;
  type: 'lesson_completed' | 'achievement_unlocked' | 'streak_milestone';
  userId: string;
  userName: string;
  description: string;
  timestamp: string;
  xpEarned: number;
}

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'reminder' | 'achievement' | 'social' | 'update';
  isRead: boolean;
  timestamp: string;
  actionUrl?: string;
}

export interface LeaderboardEntry {
  rank: number;
  userId: string;
  name: string;
  avatar?: string;
  xp: number;
  streak: number;
  level: number;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

// Navigation Types
export type RootStackParamList = {
  Onboarding: undefined;
  Main: undefined;
  Lesson: { lessonId: string };
  SkillTree: undefined;
  Profile: undefined;
  Settings: undefined;
  Leaderboard: undefined;
  StudyGroups: undefined;
  Achievements: undefined;
};

export type TabParamList = {
  Home: undefined;
  Learn: undefined;
  Practice: undefined;
  Community: undefined;
  Profile: undefined;
};
