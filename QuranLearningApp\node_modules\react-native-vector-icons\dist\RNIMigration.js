var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.default=void 0;var _extends2=_interopRequireDefault(require("@babel/runtime/helpers/extends"));var _classCallCheck2=_interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));var _createClass2=_interopRequireDefault(require("@babel/runtime/helpers/createClass"));var _possibleConstructorReturn2=_interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));var _getPrototypeOf2=_interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));var _inherits2=_interopRequireDefault(require("@babel/runtime/helpers/inherits"));var _react=_interopRequireWildcard(require("react"));var _propTypes=_interopRequireDefault(require("prop-types"));var _FontAwesome=_interopRequireDefault(require("react-native-vector-icons/FontAwesome"));var _Foundation=_interopRequireDefault(require("react-native-vector-icons/Foundation"));var _Ionicons=_interopRequireDefault(require("react-native-vector-icons/Ionicons"));var _MaterialIcons=_interopRequireDefault(require("react-native-vector-icons/MaterialIcons"));var _Zocial=_interopRequireDefault(require("react-native-vector-icons/Zocial"));var _SimpleLineIcons=_interopRequireDefault(require("react-native-vector-icons/SimpleLineIcons"));var _jsxFileName="/home/<USER>/work/react-native-vector-icons/react-native-vector-icons/RNIMigration.js";function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap(),t=new WeakMap();return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?t:r;})(e);}function _interopRequireWildcard(e,r){if(!r&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=_getRequireWildcardCache(r);if(t&&t.has(e))return t.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in e)if("default"!==u&&{}.hasOwnProperty.call(e,u)){var i=a?Object.getOwnPropertyDescriptor(e,u):null;i&&(i.get||i.set)?Object.defineProperty(n,u,i):n[u]=e[u];}return n.default=e,t&&t.set(e,n),n;}function _callSuper(t,o,e){return o=(0,_getPrototypeOf2.default)(o),(0,_possibleConstructorReturn2.default)(t,_isNativeReflectConstruct()?Reflect.construct(o,e||[],(0,_getPrototypeOf2.default)(t).constructor):o.apply(t,e));}function _isNativeReflectConstruct(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}));}catch(t){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!t;})();}var ICON_SET_MAP={fontawesome:_FontAwesome.default,foundation:_Foundation.default,ion:_Ionicons.default,material:_MaterialIcons.default,zocial:_Zocial.default,simpleline:_SimpleLineIcons.default};var Icon=exports.default=function(_PureComponent){function Icon(){var _this;(0,_classCallCheck2.default)(this,Icon);for(var _len=arguments.length,args=new Array(_len),_key=0;_key<_len;_key++){args[_key]=arguments[_key];}_this=_callSuper(this,Icon,[].concat(args));_this.iconRef=null;_this.handleComponentRef=function(ref){_this.iconRef=ref;};return _this;}(0,_inherits2.default)(Icon,_PureComponent);return(0,_createClass2.default)(Icon,[{key:"setNativeProps",value:function setNativeProps(nativeProps){if(this.iconRef){this.iconRef.setNativeProps(nativeProps);}}},{key:"render",value:function render(){var nameParts=this.props.name.split('|');var setName=nameParts[0];var name=nameParts[1];var IconSet=ICON_SET_MAP[setName];if(!IconSet){throw new Error("Invalid icon set \""+setName+"\"");}return _react.default.createElement(IconSet,(0,_extends2.default)({allowFontScaling:false,ref:this.handleComponentRef},this.props,{name:name,__self:this,__source:{fileName:_jsxFileName,lineNumber:51,columnNumber:7}}));}}]);}(_react.PureComponent);Icon.propTypes={name:_propTypes.default.string.isRequired,size:_propTypes.default.number,color:_propTypes.default.string};