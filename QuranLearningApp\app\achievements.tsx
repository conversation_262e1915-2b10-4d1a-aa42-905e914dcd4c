import React from 'react';
import { StyleSheet, ScrollView, View, Text, TouchableOpacity, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { useAppSelector } from '@/src/store';

const { width } = Dimensions.get('window');

export default function AchievementsScreen() {
  const router = useRouter();
  const achievements = useAppSelector(state => state.achievements.achievements);
  const unlockedAchievements = useAppSelector(state => state.achievements.unlockedAchievements);
  const user = useAppSelector(state => state.user.currentUser);

  const isUnlocked = (achievementId: string) => {
    return unlockedAchievements.some(a => a.id === achievementId);
  };

  const getUnlockedDate = (achievementId: string) => {
    const unlocked = unlockedAchievements.find(a => a.id === achievementId);
    return unlocked?.unlockedAt ? new Date(unlocked.unlockedAt).toLocaleDateString() : '';
  };

  const achievementCategories = [
    { id: 'completion', title: 'Completion', icon: '🎯', color: '#4CAF50' },
    { id: 'streak', title: 'Streaks', icon: '🔥', color: '#FF9800' },
    { id: 'recitation', title: 'Recitation', icon: '🎤', color: '#2196F3' },
    { id: 'speed', title: 'Speed', icon: '⚡', color: '#9C27B0' },
    { id: 'accuracy', title: 'Accuracy', icon: '💯', color: '#F44336' },
  ];

  const renderAchievement = (achievement: any) => {
    const unlocked = isUnlocked(achievement.id);
    const unlockedDate = getUnlockedDate(achievement.id);

    return (
      <View key={achievement.id} style={[styles.achievementCard, !unlocked && styles.lockedCard]}>
        <LinearGradient
          colors={unlocked ? ['#FFD700', '#FFA500'] : ['#BDBDBD', '#9E9E9E']}
          style={styles.achievementGradient}
        >
          <View style={styles.achievementContent}>
            <Text style={[styles.achievementIcon, !unlocked && styles.lockedIcon]}>
              {unlocked ? achievement.icon : '🔒'}
            </Text>
            
            <View style={styles.achievementInfo}>
              <Text style={[styles.achievementTitle, !unlocked && styles.lockedText]}>
                {unlocked ? achievement.title : '???'}
              </Text>
              <Text style={[styles.achievementDescription, !unlocked && styles.lockedText]}>
                {unlocked ? achievement.description : 'Complete more lessons to unlock'}
              </Text>
              
              {unlocked && unlockedDate && (
                <Text style={styles.unlockedDate}>Unlocked: {unlockedDate}</Text>
              )}
            </View>
            
            <View style={styles.xpBadge}>
              <Text style={styles.xpText}>+{achievement.xpReward} XP</Text>
            </View>
          </View>
        </LinearGradient>
      </View>
    );
  };

  const renderCategory = (category: any) => {
    const categoryAchievements = achievements.filter(a => a.category === category.id);
    const unlockedCount = categoryAchievements.filter(a => isUnlocked(a.id)).length;

    return (
      <View key={category.id} style={styles.categorySection}>
        <View style={styles.categoryHeader}>
          <LinearGradient
            colors={[category.color, `${category.color}CC`]}
            style={styles.categoryBadge}
          >
            <Text style={styles.categoryIcon}>{category.icon}</Text>
          </LinearGradient>
          
          <View style={styles.categoryInfo}>
            <Text style={styles.categoryTitle}>{category.title}</Text>
            <Text style={styles.categoryProgress}>
              {unlockedCount}/{categoryAchievements.length} unlocked
            </Text>
          </View>
          
          <View style={styles.progressCircle}>
            <Text style={styles.progressText}>
              {Math.round((unlockedCount / categoryAchievements.length) * 100)}%
            </Text>
          </View>
        </View>
        
        <View style={styles.achievementsList}>
          {categoryAchievements.map(renderAchievement)}
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={['#667eea', '#764ba2']}
        style={styles.header}
      >
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Achievements</Text>
          <Text style={styles.headerSubtitle}>Your accomplishments</Text>
          
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>{unlockedAchievements.length}</Text>
              <Text style={styles.statLabel}>Unlocked</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>{achievements.length}</Text>
              <Text style={styles.statLabel}>Total</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>
                {unlockedAchievements.reduce((sum, a) => sum + a.xpReward, 0)}
              </Text>
              <Text style={styles.statLabel}>XP Earned</Text>
            </View>
          </View>
        </View>
      </LinearGradient>

      {/* Achievements List */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {achievementCategories.map(renderCategory)}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    paddingTop: 60,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  backButton: {
    position: 'absolute',
    top: 60,
    left: 20,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backIcon: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerContent: {
    alignItems: 'center',
    marginTop: 20,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    marginBottom: 20,
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 15,
    padding: 15,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  statLabel: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.9)',
    marginTop: 4,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  categorySection: {
    marginBottom: 30,
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  categoryBadge: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  categoryIcon: {
    fontSize: 24,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  categoryProgress: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  progressCircle: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#333',
  },
  achievementsList: {
    gap: 10,
  },
  achievementCard: {
    borderRadius: 15,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  lockedCard: {
    opacity: 0.7,
  },
  achievementGradient: {
    padding: 15,
  },
  achievementContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  achievementIcon: {
    fontSize: 32,
    marginRight: 15,
  },
  lockedIcon: {
    opacity: 0.5,
  },
  achievementInfo: {
    flex: 1,
  },
  achievementTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 4,
  },
  achievementDescription: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    marginBottom: 4,
  },
  lockedText: {
    color: 'rgba(255, 255, 255, 0.7)',
  },
  unlockedDate: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  xpBadge: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 12,
    paddingHorizontal: 10,
    paddingVertical: 6,
  },
  xpText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
});
