import React, { useEffect } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useAppDispatch, useAppSelector } from '@/src/store';
import { setCurrentLesson } from '@/src/store/slices/lessonSlice';
import { addCompletedLesson, updateStudyTime } from '@/src/store/slices/progressSlice';
import { unlockAchievement } from '@/src/store/slices/achievementSlice';
import LessonComponent from '@/src/components/LessonComponent';
import { sampleLessons, sampleAchievements } from '@/src/data/sampleData';

export default function LessonScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const dispatch = useAppDispatch();
  
  const currentLesson = useAppSelector(state => state.lessons.currentLesson);
  const user = useAppSelector(state => state.user.currentUser);
  const completedLessons = useAppSelector(state => state.progress.userProgress?.completedLessons || []);

  useEffect(() => {
    if (id) {
      // Find the lesson by ID
      const lesson = sampleLessons.find(l => l.id === id);
      if (lesson) {
        dispatch(setCurrentLesson(lesson));
      } else {
        Alert.alert('Error', 'Lesson not found', [
          { text: 'OK', onPress: () => router.back() }
        ]);
      }
    }
  }, [id, dispatch]);

  const handleLessonComplete = () => {
    if (!currentLesson) return;

    // Mark lesson as completed
    dispatch(addCompletedLesson(currentLesson.id));
    
    // Add study time
    dispatch(updateStudyTime(currentLesson.duration));

    // Check for achievements
    checkAchievements();

    // Show completion screen
    Alert.alert(
      'Lesson Complete! 🎉',
      `You earned ${currentLesson.xpReward} XP!\n\nGreat job on completing "${currentLesson.title}".`,
      [
        {
          text: 'Continue Learning',
          onPress: () => router.back(),
        },
      ]
    );
  };

  const checkAchievements = () => {
    if (!user) return;

    // Check for first lesson achievement
    if (completedLessons.length === 0) {
      const firstLessonAchievement = sampleAchievements.find(a => a.id === 'first_lesson');
      if (firstLessonAchievement) {
        dispatch(unlockAchievement(firstLessonAchievement));
        setTimeout(() => {
          Alert.alert(
            'Achievement Unlocked! 🏆',
            `${firstLessonAchievement.title}\n${firstLessonAchievement.description}\n\n+${firstLessonAchievement.xpReward} XP`,
            [{ text: 'Awesome!' }]
          );
        }, 1000);
      }
    }

    // Check for streak achievements
    if (user.streak === 7) {
      const weekStreakAchievement = sampleAchievements.find(a => a.id === 'week_streak');
      if (weekStreakAchievement) {
        dispatch(unlockAchievement(weekStreakAchievement));
        setTimeout(() => {
          Alert.alert(
            'Achievement Unlocked! 🏆',
            `${weekStreakAchievement.title}\n${weekStreakAchievement.description}\n\n+${weekStreakAchievement.xpReward} XP`,
            [{ text: 'Amazing!' }]
          );
        }, 1500);
      }
    }
  };

  const handleExit = () => {
    Alert.alert(
      'Exit Lesson?',
      'Your progress will be saved, but you won\'t earn XP until you complete the lesson.',
      [
        { text: 'Continue Learning', style: 'cancel' },
        { text: 'Exit', onPress: () => router.back() },
      ]
    );
  };

  if (!currentLesson) {
    return <View style={styles.container} />;
  }

  return (
    <View style={styles.container}>
      <LessonComponent
        onComplete={handleLessonComplete}
        onExit={handleExit}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
