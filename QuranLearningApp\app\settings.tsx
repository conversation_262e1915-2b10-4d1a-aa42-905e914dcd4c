import React from 'react';
import { StyleSheet, ScrollView, View, Text, TouchableOpacity, Switch } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { useAppSelector, useAppDispatch } from '@/src/store';
import { updateSetting } from '@/src/store/slices/settingsSlice';
import { updatePreferences } from '@/src/store/slices/userSlice';

export default function SettingsScreen() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const settings = useAppSelector(state => state.settings);
  const user = useAppSelector(state => state.user.currentUser);

  const handleSettingChange = (key: string, value: any) => {
    dispatch(updateSetting({ key, value }));
    
    // Also update user preferences if applicable
    if (user && ['language', 'arabicFontSize', 'translationLanguage', 'notificationsEnabled'].includes(key)) {
      dispatch(updatePreferences({ [key]: value }));
    }
  };

  const settingSections = [
    {
      title: 'Display',
      items: [
        {
          key: 'theme',
          title: 'Theme',
          subtitle: 'App appearance',
          type: 'select',
          options: [
            { label: 'Auto', value: 'auto' },
            { label: 'Light', value: 'light' },
            { label: 'Dark', value: 'dark' },
          ],
          value: settings.theme,
        },
        {
          key: 'arabicFontSize',
          title: 'Arabic Font Size',
          subtitle: 'Size of Arabic text',
          type: 'select',
          options: [
            { label: 'Small', value: 'small' },
            { label: 'Medium', value: 'medium' },
            { label: 'Large', value: 'large' },
          ],
          value: settings.arabicFontSize,
        },
        {
          key: 'showTransliteration',
          title: 'Show Transliteration',
          subtitle: 'Display phonetic pronunciation',
          type: 'toggle',
          value: settings.showTransliteration,
        },
        {
          key: 'showTranslation',
          title: 'Show Translation',
          subtitle: 'Display verse meanings',
          type: 'toggle',
          value: settings.showTranslation,
        },
      ],
    },
    {
      title: 'Audio',
      items: [
        {
          key: 'reciterPreference',
          title: 'Reciter',
          subtitle: 'Preferred Quran reciter',
          type: 'select',
          options: [
            { label: 'Mishary Rashid', value: 'mishary' },
            { label: 'Abdul Rahman Al-Sudais', value: 'sudais' },
            { label: 'Saad Al-Ghamdi', value: 'ghamdi' },
            { label: 'Maher Al-Muaiqly', value: 'muaiqly' },
          ],
          value: settings.reciterPreference,
        },
        {
          key: 'soundEnabled',
          title: 'Sound Effects',
          subtitle: 'App sounds and feedback',
          type: 'toggle',
          value: settings.soundEnabled,
        },
        {
          key: 'autoPlayAudio',
          title: 'Auto-play Audio',
          subtitle: 'Automatically play verse audio',
          type: 'toggle',
          value: settings.autoPlayAudio,
        },
      ],
    },
    {
      title: 'Learning',
      items: [
        {
          key: 'language',
          title: 'App Language',
          subtitle: 'Interface language',
          type: 'select',
          options: [
            { label: 'English', value: 'en' },
            { label: 'Arabic', value: 'ar' },
            { label: 'Urdu', value: 'ur' },
            { label: 'French', value: 'fr' },
            { label: 'Spanish', value: 'es' },
          ],
          value: settings.language,
        },
        {
          key: 'translationLanguage',
          title: 'Translation Language',
          subtitle: 'Language for verse translations',
          type: 'select',
          options: [
            { label: 'English', value: 'en' },
            { label: 'Arabic', value: 'ar' },
            { label: 'Urdu', value: 'ur' },
            { label: 'French', value: 'fr' },
            { label: 'Spanish', value: 'es' },
          ],
          value: settings.translationLanguage,
        },
        {
          key: 'hapticsEnabled',
          title: 'Haptic Feedback',
          subtitle: 'Vibration feedback',
          type: 'toggle',
          value: settings.hapticsEnabled,
        },
      ],
    },
    {
      title: 'Notifications',
      items: [
        {
          key: 'notificationsEnabled',
          title: 'Enable Notifications',
          subtitle: 'Daily reminders and updates',
          type: 'toggle',
          value: settings.notificationsEnabled,
        },
        {
          key: 'dailyReminderTime',
          title: 'Daily Reminder',
          subtitle: 'Time for daily study reminder',
          type: 'time',
          value: settings.dailyReminderTime,
        },
      ],
    },
  ];

  const renderSettingItem = (item: any) => {
    switch (item.type) {
      case 'toggle':
        return (
          <View key={item.key} style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>{item.title}</Text>
              <Text style={styles.settingSubtitle}>{item.subtitle}</Text>
            </View>
            <Switch
              value={item.value}
              onValueChange={(value) => handleSettingChange(item.key, value)}
              trackColor={{ false: '#E0E0E0', true: '#4CAF50' }}
              thumbColor={item.value ? '#FFFFFF' : '#F4F3F4'}
            />
          </View>
        );
      
      case 'select':
        return (
          <TouchableOpacity key={item.key} style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>{item.title}</Text>
              <Text style={styles.settingSubtitle}>{item.subtitle}</Text>
            </View>
            <View style={styles.settingValue}>
              <Text style={styles.settingValueText}>
                {item.options.find((opt: any) => opt.value === item.value)?.label || item.value}
              </Text>
              <Text style={styles.settingArrow}>›</Text>
            </View>
          </TouchableOpacity>
        );
      
      case 'time':
        return (
          <TouchableOpacity key={item.key} style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>{item.title}</Text>
              <Text style={styles.settingSubtitle}>{item.subtitle}</Text>
            </View>
            <View style={styles.settingValue}>
              <Text style={styles.settingValueText}>{item.value}</Text>
              <Text style={styles.settingArrow}>›</Text>
            </View>
          </TouchableOpacity>
        );
      
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={['#667eea', '#764ba2']}
        style={styles.header}
      >
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Settings</Text>
          <Text style={styles.headerSubtitle}>Customize your experience</Text>
        </View>
      </LinearGradient>

      {/* Settings List */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {settingSections.map((section) => (
          <View key={section.title} style={styles.section}>
            <Text style={styles.sectionTitle}>{section.title}</Text>
            <View style={styles.sectionContent}>
              {section.items.map(renderSettingItem)}
            </View>
          </View>
        ))}

        {/* About Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>About</Text>
          <View style={styles.sectionContent}>
            <TouchableOpacity style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <Text style={styles.settingTitle}>Version</Text>
                <Text style={styles.settingSubtitle}>App version information</Text>
              </View>
              <Text style={styles.settingValueText}>1.0.0</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <Text style={styles.settingTitle}>Privacy Policy</Text>
                <Text style={styles.settingSubtitle}>How we protect your data</Text>
              </View>
              <Text style={styles.settingArrow}>›</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <Text style={styles.settingTitle}>Terms of Service</Text>
                <Text style={styles.settingSubtitle}>App usage terms</Text>
              </View>
              <Text style={styles.settingArrow}>›</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    paddingTop: 60,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  backButton: {
    position: 'absolute',
    top: 60,
    left: 20,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backIcon: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerContent: {
    alignItems: 'center',
    marginTop: 20,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  sectionContent: {
    backgroundColor: 'white',
    borderRadius: 15,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingInfo: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  settingSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  settingValue: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingValueText: {
    fontSize: 16,
    color: '#666',
    marginRight: 8,
  },
  settingArrow: {
    fontSize: 20,
    color: '#ccc',
  },
});
