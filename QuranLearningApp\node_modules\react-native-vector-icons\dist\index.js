var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"createIconSet",{enumerable:true,get:function get(){return _createIconSet.default;}});Object.defineProperty(exports,"createIconSetFromFontello",{enumerable:true,get:function get(){return _createIconSetFromFontello.default;}});Object.defineProperty(exports,"createIconSetFromIcoMoon",{enumerable:true,get:function get(){return _createIconSetFromIcomoon.default;}});Object.defineProperty(exports,"createMultiStyleIconSet",{enumerable:true,get:function get(){return _createMultiStyleIconSet.default;}});var _createIconSet=_interopRequireDefault(require("./lib/create-icon-set"));var _createMultiStyleIconSet=_interopRequireDefault(require("./lib/create-multi-style-icon-set"));var _createIconSetFromFontello=_interopRequireDefault(require("./lib/create-icon-set-from-fontello"));var _createIconSetFromIcomoon=_interopRequireDefault(require("./lib/create-icon-set-from-icomoon"));