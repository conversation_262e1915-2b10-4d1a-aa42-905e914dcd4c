import React from 'react';
import { StyleSheet, ScrollView, View, Text, TouchableOpacity, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { useAppSelector } from '@/src/store';
import { ThemedText } from '@/components/ThemedText';

const { width } = Dimensions.get('window');

export default function SkillTreeScreen() {
  const router = useRouter();
  const skillTree = useAppSelector(state => state.lessons.skillTree);
  const user = useAppSelector(state => state.user.currentUser);

  const renderSkillNode = (skill: any, index: number) => {
    const isLocked = !skill.isUnlocked;
    const isCompleted = skill.isCompleted;
    
    return (
      <TouchableOpacity
        key={skill.id}
        style={[
          styles.skillNode,
          { left: (index % 2) * (width * 0.4) + 20 },
          isLocked && styles.lockedNode,
          isCompleted && styles.completedNode,
        ]}
        disabled={isLocked}
        onPress={() => {
          // Navigate to skill lessons
          router.push(`/lesson/${skill.lessons[0]?.id || 'lesson_1'}`);
        }}
      >
        <LinearGradient
          colors={
            isCompleted 
              ? ['#4CAF50', '#2E7D32']
              : isLocked 
                ? ['#BDBDBD', '#9E9E9E']
                : ['#2196F3', '#1976D2']
          }
          style={styles.skillGradient}
        >
          <Text style={styles.skillIcon}>{skill.icon}</Text>
          <Text style={[styles.skillTitle, isLocked && styles.lockedText]}>
            {skill.title}
          </Text>
          <Text style={[styles.skillDescription, isLocked && styles.lockedText]}>
            {skill.description}
          </Text>
          
          {isCompleted && (
            <View style={styles.completedBadge}>
              <Text style={styles.completedText}>✓</Text>
            </View>
          )}
          
          {isLocked && (
            <View style={styles.lockBadge}>
              <Text style={styles.lockText}>🔒</Text>
            </View>
          )}
          
          <View style={styles.xpBadge}>
            <Text style={styles.xpText}>+{skill.xpReward} XP</Text>
          </View>
        </LinearGradient>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={['#667eea', '#764ba2']}
        style={styles.header}
      >
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Skill Tree</Text>
          <Text style={styles.headerSubtitle}>Your Learning Journey</Text>
          
          <View style={styles.levelInfo}>
            <Text style={styles.levelText}>Level {user?.level || 1}</Text>
            <Text style={styles.xpText}>{user?.xp || 0} XP</Text>
          </View>
        </View>
      </LinearGradient>

      {/* Skill Tree */}
      <ScrollView style={styles.treeContainer} showsVerticalScrollIndicator={false}>
        <View style={styles.treeContent}>
          {skillTree.map((skill, index) => renderSkillNode(skill, index))}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    paddingTop: 60,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  backButton: {
    position: 'absolute',
    top: 60,
    left: 20,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backIcon: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerContent: {
    alignItems: 'center',
    marginTop: 20,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    marginBottom: 20,
  },
  levelInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  levelText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 15,
  },
  treeContainer: {
    flex: 1,
  },
  treeContent: {
    padding: 20,
    minHeight: 800,
  },
  skillNode: {
    position: 'absolute',
    width: width * 0.35,
    marginBottom: 40,
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
  },
  lockedNode: {
    opacity: 0.6,
  },
  completedNode: {
    borderWidth: 3,
    borderColor: '#4CAF50',
  },
  skillGradient: {
    padding: 20,
    alignItems: 'center',
    minHeight: 160,
  },
  skillIcon: {
    fontSize: 40,
    marginBottom: 10,
  },
  skillTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 8,
  },
  skillDescription: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    marginBottom: 15,
  },
  lockedText: {
    color: 'rgba(255, 255, 255, 0.7)',
  },
  completedBadge: {
    position: 'absolute',
    top: 10,
    right: 10,
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
  },
  completedText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  lockBadge: {
    position: 'absolute',
    top: 10,
    right: 10,
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  lockText: {
    fontSize: 14,
  },
  xpBadge: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 12,
    paddingHorizontal: 10,
    paddingVertical: 4,
    marginTop: 10,
  },
  xpText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
});
